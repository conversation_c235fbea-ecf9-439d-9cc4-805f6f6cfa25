import type { Express } from 'express';

export interface Run {
  runId?: string;
  file: Express.Multer.File;
  filters: string;
}

export interface Batch {
  runId: string;
  prices: HotelPrices[];
  last?: boolean;
  processedHotels?: number;
}

export interface RunUpdate {
  runId: string;
  filters: string;
  hotelsCount: number;
}

export interface RunDTO {
  cities: City[];
  settings: {
    pages: number;
    batch: number;
    timeToTravel: number;
  };
}

export type City = {
  id: string;
  name: string;
  slug: string;
};

export type Price = {
  hotelId: string;
  companyId: string;
  price: number;
};

export type Company = {
  id: string;
  name: string;
};

export type Hotel = {
  id: string;
  name: string;
  hotelId: string;
  trivagoId: string;
  currentPrice: number;
  newPrice: number;
  otherPrice: number;
  tax: number;
  margin: number;
  cpa: number;
  message: string;
  success: boolean;
};

export type PageInfo = {
  hotels: Hotel[];
  companies: Company[];
  prices: Price[];
};

export type DestinationInfo = {
  hotels: Hotel[];
  companies: Company[];
  prices: Price[];
};

export type HotelCompany = {
  companyName: string;
  companyPrice: number;
};

export type HotelPrices = {
  trivagoId: string;
  hotelName: string;
  prices: HotelCompany[];
};

export type HotelsPrices = HotelPrices[];

type InventoryHotel = {
  partnerReference: string;
  partnerHotelName: string;
  partnerAddress: string;
  partnerPostcode: string;
  partnerCity: string;
  partnerCityID: string;
  partnerCountry: string;
  partnerCountryID: 'BR';
  partnerLongitude: string;
  partnerLatitude: string;
  trivagoItemID: string;
  trivagoHotelName: string;
  trivagoCity: string;
  trivagoCountry: string;
};

export type Inventory = InventoryHotel[];

export type CPAs = object[];

export type HotelPayload = {
  oth: string;
  trivagoId: string;
  hotelName: string;
  channel: 'TRIVAGO';
  currentPrice: number;
  otherPrice: number;
  cpa: number;
};

export type HotelPricesPayload = HotelPayload[];

export type Campaign = {
  id: string;
  campaign: string;
  percentage: number;
};

export type Campaigns = Campaign[];

export type PageMetrics = {
  hotelsCount: number;
  errorsCount: number;
  executionTime: number;
  hotels: Hotel[];
};

export type DestinationMetrics = {
  name: string;
  hotelsCount: number;
  errorsCount: number;
  executionTime: number;
  pages: PageMetrics[];
};

export type RunMetrics = {
  hotelsCount: number;
  errorsCount: number;
  executionTime: number;
  hotelsWithBestPrice: number;
  hotelsWithPrice: number;
  destinations: DestinationMetrics[];
  startDate?: number;
  endDate?: number;
};

export type Destination = {
  id: string;
  name: string;
  locationType: string;
  location: string;
};
