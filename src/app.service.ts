/* eslint-disable @typescript-eslint/no-var-requires */
import { Injectable } from '@nestjs/common';
import {
  Batch,
  CPAs,
  HotelPrices,
  Inventory,
  Run,
  RunMetrics,
  RunUpdate,
} from './app.types';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosResponse } from 'axios';
import { Observable, firstValueFrom } from 'rxjs';
import { aggregateHotels } from './app.utils';
import { Campaigns, HotelPricesPayload } from './app.types';
import { SupabaseClient } from '@supabase/supabase-js';
const FormData = require('form-data');

@Injectable()
export class AppService {
  constructor(
    private configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly supabaseClient: SupabaseClient,
  ) {}

  private status: 'stopped' | 'waiting' | 'running' = 'waiting';
  private runs: {
    metrics: RunMetrics;
    hotels: any;
  }[] = [];

  async run(params: Run) {
    try {
      const { data: run } = await this.supabaseClient
        .from('run')
        .insert({})
        .select()
        .single();

      console.log(run);

      firstValueFrom(
        await this.getHotelsPricesByInventory({
          runId: run.id,
          file: params.file,
          filters: params.filters,
        }),
      );

      return run;
    } catch (err) {
      throw err;
    }
  }

  async batch(params: Batch) {
    const [cpas, inventory, campaigns] = await Promise.all([
      firstValueFrom(await this.getCPAs()),
      firstValueFrom(await this.getInventory()),
      firstValueFrom(await this.getCampaigns()),
    ]);

    const hotels = aggregateHotels(
      params.prices,
      campaigns.data,
      inventory.data,
      cpas.data,
    );

    const token = (await firstValueFrom(this.login())).data;
    const response = (await firstValueFrom(this.updateRates(hotels, token)))
      .data;

    const hotelsChange = response?.hotels?.map((hotel) => {
      const change = hotels.find((h) => h.oth == hotel.oth);

      return Object({
        run_id: params.runId,
        hotel_id: hotel.oth,
        hotel_name: change.hotelName,
        price: change.currentPrice,
        target_price: change.otherPrice,
        new_tax: hotel.tax,
        new_markup: hotel.markup,
        cpa: hotel.cpa,
        success: hotel.success,
        message: hotel.message,
      });
    });

    await this.supabaseClient.from('run_hotel_change').insert(hotelsChange);

    await this.supabaseClient
      .from('run')
      .update({
        processed_hotels_count: params.processedHotels,
      })
      .eq('id', params.runId);

    if (params.last) {
      await this.supabaseClient
        .from('run')
        .update({
          ended_at: new Date(),
        })
        .eq('id', params.runId);
    }

    return response;
  }

  async update(params: RunUpdate) {
    await this.supabaseClient
      .from('run')
      .update({
        filters: params.filters,
        hotels_count: params.hotelsCount,
      })
      .eq('id', params.runId);
  }

  async getHotelsPricesByInventory(
    params: Run,
  ): Promise<Observable<AxiosResponse<HotelPrices[]>>> {
    try {
      const formData = new FormData();
      const file = params.file;

      formData.append('runId', params.runId);
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });
      formData.append('filters', params.filters);
      formData.append('batchSize', 5);

      return this.httpService.post(
        `${this.configService.get<string>('TRIVAGO_SERVICE_URL')}/upload/hotels`,
        formData,
      );
    } catch (error) {
      console.error('Error uploading hotels prices:', error);
      throw error;
    }
  }

  async getInventory(): Promise<Observable<AxiosResponse<Inventory>>> {
    return this.httpService.get(
      `${this.configService.get<string>('INTELLIGENCE_SERVICE_URL')}/inventory`,
    );
  }

  async getCPAs(): Promise<Observable<AxiosResponse<CPAs>>> {
    return this.httpService.get(
      `${this.configService.get<string>('INTELLIGENCE_SERVICE_URL')}/cpas`,
    );
  }

  async getCampaigns(): Promise<Observable<AxiosResponse<Campaigns>>> {
    return this.httpService.get(
      `${this.configService.get<string>('INTELLIGENCE_SERVICE_URL')}/campaigns`,
    );
  }

  updateRates(
    hotelsPrices: HotelPricesPayload,
    token: string,
  ): Observable<AxiosResponse> {
    return this.httpService.put(
      `${this.configService.get<string>('API_URL')}/admin/rate/by-hotel`,
      { prices: hotelsPrices },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
  }

  login(): Observable<AxiosResponse<string>> {
    return this.httpService.post(
      `${this.configService.get<string>('API_URL')}/auth/admin`,
      {},
      {
        auth: {
          username: this.configService.get<string>('OURTRIP_ADMIN_EMAIL'),
          password: this.configService.get<string>('OURTRIP_ADMIN_PASSWORD'),
        },
      },
    );
  }

  getStatus(): string {
    return this.status;
  }

  getRuns() {
    return this.runs;
  }
}
