image: atlassian/default-image:3

pipelines:
  branches:
    master:
      - step:
          runs-on:
            - self.hosted
            - linux
          name: Build
          script:
            - docker build -t $DOCKERHUB_USERNAME/$IMAGE_NAME:latest .
            - echo ${DOCKERHUB_PASSWORD} | docker login --username "$DOCKERHUB_USERNAME" --password-stdin
            - IMAGE=$DOCKERHUB_USERNAME/$IMAGE_NAME:latest
            - docker push $IMAGE
          services:
            - docker
          caches:
            - docker
      - step: &deployment-status
          name: "Deployment to k8s"
          runs-on:
            - self.hosted
            - linux
          script:
            - pipe: atlassian/aws-eks-kubectl-run:1.1.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_KEY}
                AWS_DEFAULT_REGION: ${AWS_REGION}
                CLUSTER_NAME: ourtrip
                KUBECTL_COMMAND: "rollout restart deployments/${APP_NAME} -n default"
                DEBUG: "true"            
    develop:
      - step:
          runs-on:
            - self.hosted
            - linux
          name: Build
          script:
            - docker build -t $DOCKERHUB_USERNAME/$IMAGE_NAME_HML:latest .
            - echo ${DOCKERHUB_PASSWORD} | docker login --username "$DOCKERHUB_USERNAME" --password-stdin
            - IMAGE=$DOCKERHUB_USERNAME/$IMAGE_NAME_HML:latest
            - docker push $IMAGE
          services:
            - docker
          caches:
            - docker
      - step: &deployment-status
          name: 'Deployment to k8s'
          runs-on:
            - self.hosted
            - linux
          script:
            - pipe: atlassian/aws-eks-kubectl-run:1.1.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_KEY}
                AWS_DEFAULT_REGION: ${AWS_REGION}
                CLUSTER_NAME: ourtrip
                KUBECTL_COMMAND: 'rollout restart deployments/${APP_NAME_HML} -n ourtrip-hml'
                DEBUG: 'true'
definitions:
  services:
    docker:
      memory: 2048
